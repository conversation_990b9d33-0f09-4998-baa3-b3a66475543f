{"name": "@bty/components", "type": "module", "version": "1.0.4", "description": "基础组件", "author": "", "exports": {".": "./src/index.ts", "./*": "./src/components/*/index.ts", "./markdown": {"default": "./src/components/markdown/index.ts"}, "./editor/*": {"default": "./src/components/editor/*/index.tsx"}}, "main": "./src/index.ts", "peerDependencies": {"@ant-design/icons": "^4.8.0", "ahooks": "^3.7.8", "antd": "5.20.0", "react": "catalog:", "react-dom": "catalog:"}, "dependencies": {"@bty/async-loader": "workspace:*", "@bty/constant": "workspace:*", "@bty/global-types": "workspace:*", "@bty/hooks": "workspace:*", "@bty/react-icons": "workspace:*", "@bty/util": "workspace:*", "@emotion/css": "^11.13.0", "@emotion/styled": "^11.13.0", "@onlyoffice/document-editor-react": "^1.6.0", "@radix-ui/react-popover": "^1.0.6", "@radix-ui/react-slot": "^1.0.2", "@shikijs/langs": "^3.4.2", "@shikijs/themes": "^3.4.2", "@tanstack/react-virtual": "^3.13.0", "anser": "^2.3.2", "antd-style": "^3.7.1", "class-variance-authority": "^0.7.0", "classnames": "^2.3.2", "copy-to-clipboard": "^3.3.3", "escape-carriage": "^1.3.1", "hast-util-from-html": "^2.0.1", "hast-util-sanitize": "^5.0.2", "hast-util-to-html": "^9.0.3", "hast-util-to-jsx-runtime": "^2.3.6", "is-html": "^3.1.0", "katex": "^0.16.22", "lodash-es": "^4.17.21", "papaparse": "catalog:", "polished": "^4.2.2", "prosemirror-commands": "^1.7.1", "prosemirror-history": "^1.4.1", "prosemirror-keymap": "^1.2.3", "prosemirror-model": "^1.25.1", "prosemirror-schema-basic": "^1.2.4", "prosemirror-schema-list": "^1.5.1", "prosemirror-state": "^1.4.3", "prosemirror-view": "^1.40.0", "rc-field-form": "^2.2.1", "rc-image": "^7.5.1", "rc-input": "^1.7.2", "rc-input-number": "^9.4.0", "rc-textarea": "^1.9.0", "react-hot-toast": "^2.4.1", "react-markdown": "catalog:", "react-pdf": "7.6.0", "react-pdf-highlighter": "^6.1.0", "react-syntax-highlighter": "catalog:", "rehype-katex": "catalog:", "rehype-raw": "catalog:", "remark-breaks": "catalog:", "remark-cjk-friendly": "^1.2.0", "remark-gfm": "^4.0.0", "remark-math": "^6.0.0", "remark-parse": "^11.0.0", "remark-rehype": "11.1.1", "resize-observer-polyfill": "^1.5.1", "shiki": "^3.4.2", "smooth-scrollbar": "catalog:", "tailwind-merge": "^2.6.0", "unified": "^11.0.4", "unist-util-visit": "^5.0.0", "vaul": "^1.1.2", "xgplayer": "^3.0.17"}, "devDependencies": {"@bty/tsconfig": "workspace:*", "@storybook/react": "^8.6.12", "@types/hast": "^3.0.4", "@types/lodash-es": "^4.17.8", "@types/mdast": "^4.0.4", "@types/node": "catalog:", "@types/papaparse": "catalog:", "@types/react": "catalog:", "@types/react-dom": "catalog:", "ahooks": "^3.7.8", "antd": "5.20.0", "dayjs": "1.11.13", "react": "catalog:", "react-dom": "catalog:", "typescript": "^5.0.2", "vite": "^5.0.0"}}