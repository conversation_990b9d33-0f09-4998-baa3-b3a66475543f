import type { Paragraph, Root } from 'mdast'
import { visit, SKIP } from 'unist-util-visit'
import { sanitize } from 'hast-util-sanitize'
import { fromHtml } from 'hast-util-from-html'
import { toHtml } from 'hast-util-to-html'
import isHtml from 'is-html'

export function remarkSanitize() {
  return function transformer(tree: Root) {
    visit(tree, 'html', (node, index, parent) => {
      try {
        const isXml = !isHtml(node.value)
        if (isXml) {
          console.log('====== xml value', node.value, node.value.length, index)
          // 如果是 XML 格式，转换为段落节点
          const paragraphNode: Paragraph = {
            type: 'paragraph',
            children: [
              {
                type: 'text',
                value: node.value.replace(' ', '&nbsp;'),
              },
            ],
          }
          parent.children[index] = paragraphNode as any
          return
        }

        // 使用 hast-util-from-html 解析 HTML 字符串
        const hast = fromHtml(node.value, {
          fragment: true,
        })

        if (hast.type !== 'root') {
          parent.children.splice(index, 1)
          return [SKIP, index]
        }

        const cleanHast = sanitize(hast)

        if (cleanHast.type === 'root' && cleanHast.children.length === 0) {
          // 清理后为空，移除整个节点
          parent.children.splice(index, 1)
          return [SKIP, index]
        }

        // 将清理后的 hast 转换回 HTML 字符串
        const cleanHtml = toHtml(cleanHast, {
          allowDangerousHtml: false,
          allowDangerousCharacters: false,
          closeSelfClosing: true,
          omitOptionalTags: false,
          upperDoctype: false,
          quoteSmart: true,
          preferUnquoted: false,
          space: 'html',
        })

        // 检查清理后的 HTML 是否有内容
        if (cleanHtml.trim()) {
          // 更新当前 HTML 节点的值为清理后的 HTML
          node.value = cleanHtml
          return
        }
        // 清理后为空，移除节点
        parent.children.splice(index, 1)
        return [SKIP, index]
      } catch (error) {
        // 处理失败时移除节点
        parent.children.splice(index, 1)
        return [SKIP, index]
      }
    })
  }
}
