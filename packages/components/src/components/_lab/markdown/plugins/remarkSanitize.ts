import type { Root } from 'mdast'
import { visit, SKIP } from 'unist-util-visit'
import { sanitize } from 'hast-util-sanitize'
import { fromHtml } from 'hast-util-from-html'
import { toHtml } from 'hast-util-to-html'

/**
 * 检测内容是否为 XML 格式
 * 通过检查是否包含非标准 HTML 标签来判断
 */
function isXmlContent(content: string): boolean {
  // 移除注释和 CDATA
  const cleanContent = content
    .replace(/<!--[\s\S]*?-->/g, '')
    .replace(/<!\[CDATA\[[\s\S]*?\]\]>/g, '')

  // 提取所有标签名
  const tagMatches = cleanContent.match(/<\/?([a-zA-Z][a-zA-Z0-9-_]*)/g)
  if (!tagMatches) return false

  const tagNames = tagMatches.map(match =>
    match.replace(/^<\/?/, '').toLowerCase(),
  )

  // 标准 HTML 标签列表（常用的）
  const standardHtmlTags = new Set([
    'a',
    'abbr',
    'address',
    'area',
    'article',
    'aside',
    'audio',
    'b',
    'base',
    'bdi',
    'bdo',
    'blockquote',
    'body',
    'br',
    'button',
    'canvas',
    'caption',
    'cite',
    'code',
    'col',
    'colgroup',
    'data',
    'datalist',
    'dd',
    'del',
    'details',
    'dfn',
    'dialog',
    'div',
    'dl',
    'dt',
    'em',
    'embed',
    'fieldset',
    'figcaption',
    'figure',
    'footer',
    'form',
    'h1',
    'h2',
    'h3',
    'h4',
    'h5',
    'h6',
    'head',
    'header',
    'hr',
    'html',
    'i',
    'iframe',
    'img',
    'input',
    'ins',
    'kbd',
    'label',
    'legend',
    'li',
    'link',
    'main',
    'map',
    'mark',
    'meta',
    'meter',
    'nav',
    'noscript',
    'object',
    'ol',
    'optgroup',
    'option',
    'output',
    'p',
    'param',
    'picture',
    'pre',
    'progress',
    'q',
    'rp',
    'rt',
    'ruby',
    's',
    'samp',
    'script',
    'section',
    'select',
    'small',
    'source',
    'span',
    'strong',
    'style',
    'sub',
    'summary',
    'sup',
    'table',
    'tbody',
    'td',
    'template',
    'textarea',
    'tfoot',
    'th',
    'thead',
    'time',
    'title',
    'tr',
    'track',
    'u',
    'ul',
    'var',
    'video',
    'wbr',
  ])

  // 如果包含非标准 HTML 标签，则认为是 XML
  return tagNames.some(tag => !standardHtmlTags.has(tag))
}

export function remarkSanitize() {
  return function transformer(tree: Root) {
    visit(tree, 'html', (node, index, parent) => {
      try {
        // 检测是否为 XML 格式（包含自定义标签）
        const isXmlLike = this.isXmlContent(node.value)
        if (isXmlLike) {
          // 如果是 XML 格式，跳过 sanitize 处理
          return
        }
        // 使用 hast-util-from-html 解析 HTML 字符串
        const hast = fromHtml(node.value, {
          fragment: true,
        })

        if (hast.type !== 'root') {
          parent.children.splice(index, 1)
          return [SKIP, index]
        }

        const cleanHast = sanitize(hast)

        if (cleanHast.type === 'root' && cleanHast.children.length === 0) {
          // 清理后为空，移除整个节点
          parent.children.splice(index, 1)
          return [SKIP, index]
        }
        // 将清理后的 hast 转换回 HTML 字符串
        const cleanHtml = toHtml(cleanHast, {
          allowDangerousHtml: false,
          allowDangerousCharacters: false,
          closeSelfClosing: true,
          omitOptionalTags: false,
          upperDoctype: false,
          quoteSmart: true,
          preferUnquoted: false,
          space: 'html',
        })

        console.log('====== cleanHtml', cleanHtml)

        // 检查清理后的 HTML 是否有内容
        if (cleanHtml.trim()) {
          // 更新当前 HTML 节点的值为清理后的 HTML
          node.value = cleanHtml
          return
        }
        // 清理后为空，移除节点
        parent.children.splice(index, 1)
        return [SKIP, index]
      } catch (error) {
        // 处理失败时移除节点
        parent.children.splice(index, 1)
        return [SKIP, index]
      }
    })
  }
}
