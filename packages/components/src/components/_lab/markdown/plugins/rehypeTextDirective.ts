import { visit } from 'unist-util-visit'
import type { Root, Element } from 'hast'

/**
 * rehype 插件：处理 textDirective 节点，将 cite 类型转换为 HTML cite 元素
 */
export function rehypeTextDirective() {
  return function transformer(tree: Root) {
    visit(tree, 'element', (node: Element, index, parent) => {
      // 检查是否是 textDirective 类型的节点
      if (
        node.tagName === 'textDirective' &&
        node.properties?.name === 'cite'
      ) {
        // 提取 cite 内容
        const content =
          node.children?.[0]?.type === 'text'
            ? (node.children[0] as any).value || ''
            : ''

        // 创建新的 cite 元素
        const citeElement: Element = {
          type: 'element',
          tagName: 'cite',
          properties: {
            'data-ref': content,
            className: [
              'citation',
              'ui-mx-[2px]',
              'ui-cursor-pointer',
              'ui-inline-block',
              'ui-w-[14px]',
              'ui-h-[14px]',
              'ui-text-center',
              'ui-text-[12px]',
              'ui-leading-[14px]',
              'ui-bg-[#626999]',
              'ui-bg-opacity-[0.12]',
              'ui-rounded-[4px]',
              'ui-text-[#b2b5ce]',
            ],
          },
          children: [
            {
              type: 'text',
              value: content,
            },
          ],
        }

        // 替换原节点
        if (parent && typeof index === 'number') {
          parent.children[index] = citeElement
        }
      }
    })
  }
}
