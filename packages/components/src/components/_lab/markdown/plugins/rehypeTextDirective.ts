import { visit } from 'unist-util-visit'
import type { Root, Element } from 'hast'
import type { Data, Parent, PhrasingContent } from 'mdast'

interface TextDirectiveData extends Data {}

interface DirectiveFields {
  /**
   * Directive attributes.
   */
  attributes?: Record<string, string | null | undefined> | null | undefined

  /**
   * Directive name.
   */
  name: string
}

interface TextDirective extends DirectiveFields, Parent {
  /**
   * Node type of text directive.
   */
  type: 'textDirective'

  /**
   * Children of text directive.
   */
  children: Array<PhrasingContent>

  /**
   * Data associated with the text leaf directive.
   */
  data?: TextDirectiveData | undefined
}

/**
 * rehype 插件：处理通过 passThrough 传递的 textDirective 节点，将 cite 类型转换为 HTML cite 元素
 */
export function rehypeTextDirective() {
  return function transformer(tree: Root) {
    visit(
      tree,
      'textDirective',
      (node: TextDirective, index, parent: Element) => {
        if (node.name === 'cite') {
          // 提取 cite 内容（从 mdast textDirective 节点）
          const content =
            node.children?.[0]?.type === 'text'
              ? node.children[0].value || ''
              : ''

          // 创建新的 hast cite 元素
          const citeElement: Element = {
            type: 'element',
            tagName: 'cite',
            properties: {
              'data-ref': content,
            },
            children: [
              {
                type: 'text',
                value: content,
              },
            ],
          }

          // 替换原节点
          if (parent && typeof index === 'number') {
            parent.children[index] = citeElement
          }
        }
      },
    )
  }
}
