import type { Root } from 'hast'
import { cn } from '@bty/util'
import React, { useMemo, useState, useEffect, useImperativeHandle } from 'react'
import { unified } from 'unified'
import remarkParse from 'remark-parse'
import remarkGfm from 'remark-gfm'
import remarkRehype from 'remark-rehype'
import remarkMath from 'remark-math'
import remarkCjkFriendly from 'remark-cjk-friendly'
import remarkBreaks from 'remark-breaks'
import rehypeRaw from 'rehype-raw'
import { escapeBrackets } from '../../markdown/utils/utils'
import type { ClassNames } from '../_utils/styleUtils'
import { normalizeMarkdown } from './utils/normalizeMarkdown'
import { AstRenderer } from './renderer/AstRenderer'
import {
  type RendererType,
  RendererProvider,
} from './renderer/RendererProvider'
import { remarkCite } from './plugins/remarkCite'
import { Fallback } from './Fallback'
import './style/markdown.css'
import { remarkSanitize } from './plugins/remarkSanitize'

export interface MarkdownProps {
  overrides?: {
    root?: ClassNames
  }
  /**
   * 需要解析的 markdown 内容
   */
  content?: string
  /**
   * 从远端下载文件内容并解析，优先级高于 `content` 和 `children`
   */
  url?: string
  /**
   * 每一个节点的渲染器
   */
  as?: RendererType
  /**
   * 是否允许复制代码块内容
   * @default false
   */
  codeCopyable?: boolean
  /**
   * 是否支持图片预览
   * @default true
   */
  imagePreviewable?: boolean
  /**
   * 加载中指示器
   * @default null
   */
  loadingIndicator?: React.ReactNode
  /**
   * 加载失败时显示的组件
   * @default <Fallback />
   */
  fallback?: React.ReactNode
  /**
   * 是否允许解析 XML 内容
   * @default true
   */
  allowXml?: boolean
  /**
   * 链接点击事件处理函数
   * @param url 点击的链接地址
   * @param event 点击事件对象
   */
  onLinkClick?: (
    url: string,
    event: React.MouseEvent<HTMLAnchorElement>,
  ) => void
  /**
   * 需要解析的 markdown 内容
   */
  children?: string
}

export interface MarkdownRef {
  getContent: () => string | undefined
}

const AstFallback = {
  ast: {
    children: [],
  },
}

function InnerMarkdown(
  {
    overrides,
    content: contentProp,
    url,
    as,
    codeCopyable = false,
    imagePreviewable = true,
    loadingIndicator = null,
    fallback = <Fallback />,
    onLinkClick,
    children: childrenProp,
  }: MarkdownProps,
  ref: React.Ref<MarkdownRef>,
) {
  const remote = !!url

  const [loading, setLoading] = useState(false)

  const [content, setContent] = useState(
    remote ? undefined : contentProp || childrenProp || '',
  )

  const [error, setError] = useState(false)

  useEffect(() => {
    // 设置300ms延迟才显示loading状态
    let isCompleted = false
    let loadingTimer: number | null = null

    if (remote) {
      setError(false)

      loadingTimer = window.setTimeout(() => {
        if (!isCompleted) {
          setLoading(true)
        }
      }, 300)

      fetch(url)
        .then(response => response.text())
        .then(setContent)
        .catch(() => setError(true))
        .finally(() => {
          setLoading(false)
          isCompleted = true
          if (loadingTimer) {
            window.clearTimeout(loadingTimer)
          }
        })
    }

    // 确保清理函数总是被返回，即使没有执行fetch操作
    return () => {
      isCompleted = true
      if (loadingTimer) {
        window.clearTimeout(loadingTimer)
      }
    }
  }, [remote, url])

  useEffect(() => {
    if (remote) {
      return
    }
    setContent(contentProp || '')
  }, [contentProp])

  // 转义后的内容，防止Markdown语法冲突
  const normalizedContent = useMemo(
    () => escapeBrackets(normalizeMarkdown(content)),
    [content],
  )

  const { ast } = useMemo(() => {
    if (error) {
      return AstFallback
    }

    const processor = unified()
      .use(remarkParse, { position: false })
      .use(remarkCite)
      .use(remarkSanitize)
      .use(remarkGfm, { singleTilde: false })
      .use(remarkMath, { singleDollarTextMath: false })
      .use(remarkBreaks)
      .use(remarkCjkFriendly)
      .use(remarkRehype, {
        allowDangerousHtml: true,
        unknownHandler: (state, node) => {
          if (node.type === 'textDirective') {
            
          }
          return undefined
        },
      })
      .use(rehypeRaw)

    try {
      const ast = processor.runSync(processor.parse(normalizedContent))
      return { ast }
    } catch (error) {
      return AstFallback
    }
  }, [error, normalizedContent])

  useImperativeHandle(ref, () => ({
    getContent: () => {
      return content
    },
  }))

  return (
    <div className={cn('next-markdown', overrides?.root)}>
      {loading && loadingIndicator}
      {ast === AstFallback.ast && fallback}
      <RendererProvider
        renderer={as}
        rendererOptions={{
          code: {
            copyable: codeCopyable,
          },
          image: {
            previewable: imagePreviewable,
          },
          anchor: {
            onClick: onLinkClick,
          },
        }}
      >
        <AstRenderer ast={ast as Root} />
      </RendererProvider>
    </div>
  )
}

export const Markdown = React.forwardRef(InnerMarkdown)
