export function ParagraphRenderer({
  node,
  children,
}: {
  children?: React.ReactNode
}) {
  console.log('====== node', node, `-${children}-`)
  // break-words 处理换行，不然有横向滚动条
  // 使用 CSS 选择器：当 p 是 li 的子元素时移除边距
  return (
    <p className='my-4 break-words whitespace-pre-wrap [word-break:break-word] [:is(li)_&]:leading-[1.75] [:is(li)_&]:mt-0 [:is(li)_&]:mb-[0.5em]'>
      {children}
    </p>
  )
}
