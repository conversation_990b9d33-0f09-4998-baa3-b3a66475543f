import React from 'react'
import type { Element } from 'hast'
import { use<PERSON><PERSON><PERSON> } from './RendererProvider'

export function CiteRenderer({
  node: _,
  children,
  ...restProps
}: React.PropsWithChildren<{ node?: Element }>) {
  const { renderer } = useRenderer()

  const cite = renderer?.cite

  if (Array.isArray(cite)) {
    const [Cite, props] = cite
    return React.createElement(Cite, { ...props, ...restProps }, children)
  }

  if (cite) {
    return React.createElement(cite, { ...restProps }, children)
  }

  return (
    <cite
      className='citation ui-mx-[2px] ui-cursor-pointer ui-inline-block ui-w-[14px] ui-h-[14px] ui-text-center ui-text-[12px] ui-leading-[14px] ui-bg-[#626999] ui-bg-opacity-[0.12] ui-rounded-[4px] ui-text-[#b2b5ce]'
      {...restProps}
    >
      {children}
    </cite>
  )
}
