import React from 'react'
import type { Root } from 'hast'
import { toJsxRuntime } from 'hast-util-to-jsx-runtime'
import { Fragment, jsx, jsxs } from 'react/jsx-runtime'
import type { HeadingRendererProps } from './HeadingRenderer'
import { HeadingRenderer } from './HeadingRenderer'
import { ParagraphRenderer } from './ParagraphRenderer'
import { TableRenderer } from './TableRenderer'
import { ListRenderer } from './ListRenderer'
import { StrongRenderer } from './StrongRenderer'
import { AnchorRenderer } from './AnchorRenderer'
import { CodeRenderer } from './CodeRenderer'
import { ImageRenderer } from './ImageRenderer'
import { CiteRenderer } from './CiteRenderer'

function createHeadingComponent(depth: HeadingRendererProps['depth']) {
  return ({ children }: { children: React.ReactNode }) => (
    <HeadingRenderer depth={depth}>{children}</HeadingRenderer>
  )
}

const headingComponents = Object.fromEntries(
  ([1, 2, 3, 4, 5, 6] as const).map(depth => [
    `h${depth}`,
    createHeadingComponent(depth),
  ]),
)

function InnerAstRenderer({ ast }: { ast: Root }) {
  const children = toJsxRuntime(ast, {
    Fragment,
    components: {
      ...headingComponents,

      p: ParagraphRenderer,

      // table
      table: TableRenderer,
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-expect-error
      thead: TableRenderer.Header,
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-expect-error
      tbody: TableRenderer.Body,
      tr: TableRenderer.Row,
      th: TableRenderer.Head,
      td: TableRenderer.Cell,

      // list
      ul: ({ children }) => (
        <ListRenderer ordered={false}>{children}</ListRenderer>
      ),
      ol: ({ start, children }) => (
        <ListRenderer start={start} ordered>
          {children}
        </ListRenderer>
      ),
      li: ({ className, children }) => (
        <ListRenderer.Item
          checked={className && className === 'task-list-item'}
        >
          {children}
        </ListRenderer.Item>
      ),
      input: ListRenderer.Checkbox,

      strong: StrongRenderer,

      a: AnchorRenderer,

      img: ImageRenderer,

      pre: CodeRenderer.Pre,
      code: CodeRenderer,

      hr: () => <hr className='my-8 border-none h-px bg-[#d1d9e0]' />,

      blockquote: ({ children }) => (
        <blockquote className='m-0 px-[1em] text-[#59636e] border-l-[0.25em] border-solid border-[#d1d9e0]'>
          {children}
        </blockquote>
      ),

      cite: CiteRenderer,
    },
    ignoreInvalidStyle: true,
    passNode: true,
    jsx,
    jsxs,
  })

  // const children = nodes.map((node, index) => {
  //   switch (node.type) {
  //     case 'html':
  //       return (
  //         <div
  //           key={`html-${index}`}
  //           dangerouslySetInnerHTML={{ __html: node.value }}
  //         />
  //       )
  //     case 'math':
  //       return <MathRenderer key={`math-${index}`} ast={node} />
  //     default:
  //       return null
  //   }
  // })

  return <>{children}</>
}

export const AstRenderer = React.memo(InnerAstRenderer)
