import type { FC, ReactNode } from 'react'
import { memo, useCallback } from 'react'
import type { FormConfig } from '@bty/global-types/form'
import { cn } from '@bty/util'

export interface ShortcutsCardProps {
  icon: ReactNode
  title: string
  id?: string
  formConfig: FormConfig[]
  className?: string
  onClick?: (item: Omit<ShortcutsCardProps, 'onClick'>) => void
}

export const ShortcutsCard: FC<ShortcutsCardProps> = memo(
  ({ onClick, ...restProps }) => {
    const { icon, title, className } = restProps
    const handleClick = useCallback(() => {
      onClick?.(restProps)
    }, [restProps, onClick])

    return (
      <div
        onClick={handleClick}
        className={cn(
          'rounded-[8px] hover:bg-[#626999] hover:bg-opacity-[0.08]  bg-[white] border py-[6px] px-[8px] inline-flex items-center text-[12px] cursor-pointer  border-solid  border-[#E1E1E5] border-opacity-[0.6]',
          className,
        )}
      >
        <div>{icon}</div>
        <div className='ml-[6px]'>{title}</div>
      </div>
    )
  },
)
