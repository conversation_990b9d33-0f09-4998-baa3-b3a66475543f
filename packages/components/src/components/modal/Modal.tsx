import type { ModalProps } from 'antd'
import { Modal as AntdModal, Space } from 'antd'
import styled from '@emotion/styled'
import { Button } from '../button'
import { IconFont } from '../icon-font'

const StyledModal = styled(AntdModal)`
  .ant-modal-header {
    margin-bottom: 0;
  }
`
const FooterWrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 16px;
  border-top: 1px solid rgba(0, 0, 0, 0.08); /* 假设 border-font_1 是黑色 */
  border-color: '#8D8D9'; /* 使用自定义 CSS 变量 */
  opacity: 0.08;
`

type BaseModalProps = ModalProps & {
  extraFooter?: React.ReactNode
  footerClassName?: string
  hiddenFooter?: boolean
}

export function Modal(props: BaseModalProps) {
  const onCancel = (e: any) => {
    props.onCancel?.(e)
  }
  const onOk = (e: any) => {
    props.onOk?.(e)
  }
  const Footer = (
    <FooterWrapper className={props.footerClassName}>
      {props.extraFooter}
      <Space>
        <Button onClick={onCancel} {...props.cancelButtonProps}>
          {props.cancelText || '取消'}
        </Button>
        <Button
          onClick={onOk}
          type={(props.okType as any) ?? 'primary'}
          {...props.okButtonProps}
        >
          {props.okText || '确定'}
        </Button>
      </Space>
    </FooterWrapper>
  )

  const header = (
    <div className='p-16px border-b border-solid border-font_1 border-opacity-8 line-height-16px'>
      {props.title}
    </div>
  )

  return (
    <StyledModal
      centered
      footer={props.hiddenFooter ? null : (props.footer ?? Footer)}
      {...props}
      wrapClassName={`keyu-modal ${props.wrapClassName ?? ''}`}
      styles={{
        body: {
          padding: 16,
          ...(props.styles?.body ?? props.bodyStyle ?? {}),
        },
        mask: {
          ...(props.styles?.mask ?? props.maskStyle ?? {}),
          backgroundColor: 'transparent',
          backdropFilter: 'blur(16px)',
        },
        content: {
          ...props.styles?.content,
        },
      }}
      closeIcon={<IconFont name='guanbi' />}
      title={typeof props.title === 'string' ? header : props.title}
    >
      {props.children}
    </StyledModal>
  )
}
